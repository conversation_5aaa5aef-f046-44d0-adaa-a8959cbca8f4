/**
 * Predictive Monitoring Test Framework
 * 
 * Comprehensive testing framework for predictive monitoring capabilities
 * including forecasting, anomaly detection, and alert generation.
 */

const { EnhancedAnomalyDetectionService, ALGORITHMS, SEVERITY_LEVELS } = require('../services/monitoring/enhanced-anomaly-detection');
const { PredictiveAlertManager, ALERT_TYPES, ALERT_SEVERITIES } = require('../services/monitoring/predictive-alert-manager');

const logger = require('../utils/logger').getLogger('predictive-monitoring-test');

// Test data generators
class TestDataGenerator {
  /**
   * Generate normal time series data
   * @param {number} length - Number of data points
   * @param {Object} options - Generation options
   * @returns {Array<Object>} Time series data
   */
  static generateNormalTimeSeries(length = 100, options = {}) {
    const {
      baseValue = 50,
      variance = 10,
      trend = 0,
      seasonality = false,
      seasonalPeriod = 24
    } = options;
    
    const data = [];
    const startTime = Date.now() - (length * 60 * 1000); // 1 minute intervals
    
    for (let i = 0; i < length; i++) {
      let value = baseValue + (trend * i);
      
      // Add seasonal component
      if (seasonality) {
        const seasonalComponent = Math.sin((2 * Math.PI * i) / seasonalPeriod) * (variance * 0.5);
        value += seasonalComponent;
      }
      
      // Add random noise
      value += (Math.random() - 0.5) * variance;
      
      data.push({
        timestamp: new Date(startTime + (i * 60 * 1000)).toISOString(),
        value: Math.max(0, value)
      });
    }
    
    return data;
  }
  
  /**
   * Generate time series with anomalies
   * @param {number} length - Number of data points
   * @param {Object} options - Generation options
   * @returns {Array<Object>} Time series data with anomalies
   */
  static generateAnomalousTimeSeries(length = 100, options = {}) {
    const {
      anomalyCount = 5,
      anomalyMagnitude = 3,
      anomalyType = 'point'
    } = options;
    
    const normalData = this.generateNormalTimeSeries(length, options);
    const anomalyIndices = new Set();
    
    // Select random indices for anomalies
    while (anomalyIndices.size < anomalyCount) {
      const index = Math.floor(Math.random() * length);
      anomalyIndices.add(index);
    }
    
    // Inject anomalies
    anomalyIndices.forEach(index => {
      if (anomalyType === 'point') {
        // Point anomaly - single outlier
        normalData[index].value *= anomalyMagnitude;
      } else if (anomalyType === 'collective') {
        // Collective anomaly - group of outliers
        for (let i = index; i < Math.min(index + 3, length); i++) {
          normalData[i].value *= anomalyMagnitude;
        }
      }
    });
    
    return normalData;
  }
  
  /**
   * Generate time series with trend change
   * @param {number} length - Number of data points
   * @param {Object} options - Generation options
   * @returns {Array<Object>} Time series data with trend change
   */
  static generateTrendChangeTimeSeries(length = 100, options = {}) {
    const {
      changePoint = Math.floor(length / 2),
      initialTrend = 0,
      newTrend = 1
    } = options;
    
    const data = [];
    const startTime = Date.now() - (length * 60 * 1000);
    let baseValue = 50;
    
    for (let i = 0; i < length; i++) {
      const trend = i < changePoint ? initialTrend : newTrend;
      baseValue += trend;
      
      // Add noise
      const value = baseValue + (Math.random() - 0.5) * 5;
      
      data.push({
        timestamp: new Date(startTime + (i * 60 * 1000)).toISOString(),
        value: Math.max(0, value)
      });
    }
    
    return data;
  }
}

/**
 * Test suite for predictive monitoring
 */
class PredictiveMonitoringTestSuite {
  constructor() {
    this.results = {
      total: 0,
      passed: 0,
      failed: 0,
      tests: []
    };
    
    this.anomalyDetectionService = new EnhancedAnomalyDetectionService({
      outputPath: '/tmp/test-anomalies',
      visualizationEnabled: false
    });
    
    this.alertManager = new PredictiveAlertManager({
      outputPath: '/tmp/test-alerts',
      notificationChannels: []
    });
  }
  
  /**
   * Run all predictive monitoring tests
   * @returns {Promise<Object>} Test results
   */
  async runAllTests() {
    logger.info('Starting predictive monitoring test suite');
    
    // Test anomaly detection algorithms
    await this.testAnomalyDetectionAlgorithms();
    
    // Test anomaly detection accuracy
    await this.testAnomalyDetectionAccuracy();
    
    // Test alert generation
    await this.testAlertGeneration();
    
    // Test alert management
    await this.testAlertManagement();
    
    // Test performance
    await this.testPerformance();
    
    // Test edge cases
    await this.testEdgeCases();
    
    logger.info(`Test suite completed. Passed: ${this.results.passed}/${this.results.total}`);
    
    return this.results;
  }
  
  /**
   * Test anomaly detection algorithms
   */
  async testAnomalyDetectionAlgorithms() {
    // Test Z-Score algorithm
    await this.runTest('Z-Score Anomaly Detection', async () => {
      const data = TestDataGenerator.generateAnomalousTimeSeries(100, {
        anomalyCount: 5,
        anomalyMagnitude: 4
      });
      
      const anomalies = await this.anomalyDetectionService.detectAnomalies(data, {
        algorithms: [ALGORITHMS.Z_SCORE],
        metric: 'test_metric'
      });
      
      this.assert(anomalies.length > 0, 'Should detect anomalies');
      this.assert(anomalies.every(a => a.algorithm === ALGORITHMS.Z_SCORE), 'All anomalies should use Z-Score algorithm');
    });
    
    // Test MAD algorithm
    await this.runTest('MAD Anomaly Detection', async () => {
      const data = TestDataGenerator.generateAnomalousTimeSeries(100, {
        anomalyCount: 3,
        anomalyMagnitude: 3
      });
      
      const anomalies = await this.anomalyDetectionService.detectAnomalies(data, {
        algorithms: [ALGORITHMS.MAD],
        metric: 'test_metric'
      });
      
      this.assert(anomalies.length > 0, 'Should detect anomalies');
      this.assert(anomalies.every(a => a.algorithm === ALGORITHMS.MAD), 'All anomalies should use MAD algorithm');
    });
    
    // Test IQR algorithm
    await this.runTest('IQR Anomaly Detection', async () => {
      const data = TestDataGenerator.generateAnomalousTimeSeries(100, {
        anomalyCount: 4,
        anomalyMagnitude: 5
      });
      
      const anomalies = await this.anomalyDetectionService.detectAnomalies(data, {
        algorithms: [ALGORITHMS.IQR],
        metric: 'test_metric'
      });
      
      this.assert(anomalies.length > 0, 'Should detect anomalies');
      this.assert(anomalies.every(a => a.algorithm === ALGORITHMS.IQR), 'All anomalies should use IQR algorithm');
    });
    
    // Test seasonal anomaly detection
    await this.runTest('Seasonal Anomaly Detection', async () => {
      const data = TestDataGenerator.generateNormalTimeSeries(100, {
        seasonality: true,
        seasonalPeriod: 24
      });
      
      // Inject seasonal anomaly
      data[50].value *= 3;
      
      const anomalies = await this.anomalyDetectionService.detectAnomalies(data, {
        algorithms: [ALGORITHMS.SEASONAL_DECOMPOSE],
        metric: 'test_metric'
      });
      
      this.assert(anomalies.length > 0, 'Should detect seasonal anomalies');
    });
  }
  
  /**
   * Test anomaly detection accuracy
   */
  async testAnomalyDetectionAccuracy() {
    await this.runTest('Anomaly Detection Accuracy', async () => {
      const anomalyCount = 10;
      const data = TestDataGenerator.generateAnomalousTimeSeries(200, {
        anomalyCount,
        anomalyMagnitude: 4
      });
      
      const anomalies = await this.anomalyDetectionService.detectAnomalies(data, {
        algorithms: [ALGORITHMS.Z_SCORE, ALGORITHMS.MAD, ALGORITHMS.IQR],
        metric: 'test_metric'
      });
      
      // Should detect at least 50% of injected anomalies
      this.assert(anomalies.length >= anomalyCount * 0.5, 'Should detect at least 50% of anomalies');
      
      // Should not have too many false positives (less than 20% of total points)
      this.assert(anomalies.length < data.length * 0.2, 'Should not have excessive false positives');
    });
    
    await this.runTest('Normal Data False Positive Rate', async () => {
      const data = TestDataGenerator.generateNormalTimeSeries(200);
      
      const anomalies = await this.anomalyDetectionService.detectAnomalies(data, {
        algorithms: [ALGORITHMS.Z_SCORE],
        metric: 'test_metric'
      });
      
      // Should have very low false positive rate for normal data
      this.assert(anomalies.length < data.length * 0.05, 'False positive rate should be less than 5%');
    });
  }
  
  /**
   * Test alert generation
   */
  async testAlertGeneration() {
    await this.runTest('Predictive Alert Generation', async () => {
      const prediction = {
        value: 95,
        confidence: 0.8,
        algorithm: 'exponential-smoothing',
        timestamp: new Date().toISOString()
      };
      
      const alert = await this.alertManager.generatePredictiveAlert(prediction, {
        metric: 'cpu_usage',
        threshold: 80,
        timeframe: '1 hour',
        templateKey: 'predictive_cpu_high'
      });
      
      this.assert(alert !== null, 'Should generate alert');
      this.assert(alert.type === ALERT_TYPES.PREDICTIVE, 'Should be predictive alert');
      this.assert(alert.severity === ALERT_SEVERITIES.WARNING, 'Should have warning severity');
      this.assert(alert.metric === 'cpu_usage', 'Should have correct metric');
    });
    
    await this.runTest('Anomaly Alert Generation', async () => {
      const anomaly = {
        timestamp: new Date().toISOString(),
        value: 150,
        score: 4.5,
        severity: 'high',
        type: 'point',
        confidence: 0.9,
        algorithm: 'z-score'
      };
      
      const alert = await this.alertManager.generateAnomalyAlert(anomaly, {
        metric: 'response_time'
      });
      
      this.assert(alert !== null, 'Should generate alert');
      this.assert(alert.type === ALERT_TYPES.ANOMALY, 'Should be anomaly alert');
      this.assert(alert.metric === 'response_time', 'Should have correct metric');
    });
    
    await this.runTest('Critical Anomaly Alert', async () => {
      const criticalAnomaly = {
        timestamp: new Date().toISOString(),
        value: 200,
        score: 8.0,
        severity: 'critical',
        type: 'point',
        confidence: 0.95,
        algorithm: 'z-score'
      };
      
      const alert = await this.alertManager.generateAnomalyAlert(criticalAnomaly, {
        metric: 'error_rate'
      });
      
      this.assert(alert !== null, 'Should generate alert');
      this.assert(alert.severity === ALERT_SEVERITIES.CRITICAL, 'Should have critical severity');
    });
  }
  
  /**
   * Test alert management
   */
  async testAlertManagement() {
    await this.runTest('Alert Acknowledgment', async () => {
      const prediction = {
        value: 85,
        confidence: 0.7,
        algorithm: 'linear-regression',
        timestamp: new Date().toISOString()
      };
      
      const alert = await this.alertManager.generatePredictiveAlert(prediction, {
        metric: 'memory_usage',
        threshold: 80
      });
      
      const acknowledgedAlert = this.alertManager.acknowledgeAlert(alert.id, 'test-user');
      
      this.assert(acknowledgedAlert.state === 'acknowledged', 'Alert should be acknowledged');
      this.assert(acknowledgedAlert.acknowledgedBy === 'test-user', 'Should track who acknowledged');
    });
    
    await this.runTest('Alert Resolution', async () => {
      const prediction = {
        value: 90,
        confidence: 0.8,
        algorithm: 'exponential-smoothing',
        timestamp: new Date().toISOString()
      };
      
      const alert = await this.alertManager.generatePredictiveAlert(prediction, {
        metric: 'disk_usage',
        threshold: 85
      });
      
      const resolvedAlert = this.alertManager.resolveAlert(alert.id, 'test-user');
      
      this.assert(resolvedAlert.state === 'resolved', 'Alert should be resolved');
      this.assert(resolvedAlert.resolvedBy === 'test-user', 'Should track who resolved');
    });
    
    await this.runTest('Alert Suppression', async () => {
      const prediction = {
        value: 95,
        confidence: 0.9,
        algorithm: 'exponential-smoothing',
        timestamp: new Date().toISOString()
      };
      
      // Generate first alert
      const alert1 = await this.alertManager.generatePredictiveAlert(prediction, {
        metric: 'cpu_usage',
        threshold: 80
      });
      
      // Generate similar alert immediately (should be suppressed)
      const alert2 = await this.alertManager.generatePredictiveAlert(prediction, {
        metric: 'cpu_usage',
        threshold: 80
      });
      
      this.assert(alert1 !== null, 'First alert should be generated');
      this.assert(alert2 === null, 'Second alert should be suppressed');
    });
  }
  
  /**
   * Test performance
   */
  async testPerformance() {
    await this.runTest('Large Dataset Performance', async () => {
      const largeDataset = TestDataGenerator.generateNormalTimeSeries(10000);
      
      const startTime = Date.now();
      const anomalies = await this.anomalyDetectionService.detectAnomalies(largeDataset, {
        algorithms: [ALGORITHMS.Z_SCORE],
        metric: 'performance_test'
      });
      const endTime = Date.now();
      
      const processingTime = endTime - startTime;
      
      this.assert(processingTime < 5000, 'Should process 10k points in less than 5 seconds');
      this.assert(Array.isArray(anomalies), 'Should return valid anomaly array');
    });
    
    await this.runTest('Multiple Algorithm Performance', async () => {
      const data = TestDataGenerator.generateAnomalousTimeSeries(1000, {
        anomalyCount: 20
      });
      
      const startTime = Date.now();
      const anomalies = await this.anomalyDetectionService.detectAnomalies(data, {
        algorithms: [ALGORITHMS.Z_SCORE, ALGORITHMS.MAD, ALGORITHMS.IQR, ALGORITHMS.SEASONAL_DECOMPOSE],
        metric: 'multi_algorithm_test'
      });
      const endTime = Date.now();
      
      const processingTime = endTime - startTime;
      
      this.assert(processingTime < 3000, 'Should process with multiple algorithms in less than 3 seconds');
      this.assert(anomalies.length > 0, 'Should detect anomalies');
    });
  }
  
  /**
   * Test edge cases
   */
  async testEdgeCases() {
    await this.runTest('Empty Dataset', async () => {
      const anomalies = await this.anomalyDetectionService.detectAnomalies([], {
        metric: 'empty_test'
      });
      
      this.assert(Array.isArray(anomalies), 'Should return array for empty dataset');
      this.assert(anomalies.length === 0, 'Should return empty array for empty dataset');
    });
    
    await this.runTest('Insufficient Data', async () => {
      const smallDataset = TestDataGenerator.generateNormalTimeSeries(5);
      
      const anomalies = await this.anomalyDetectionService.detectAnomalies(smallDataset, {
        metric: 'small_dataset_test'
      });
      
      this.assert(Array.isArray(anomalies), 'Should return array for small dataset');
      this.assert(anomalies.length === 0, 'Should return empty array for insufficient data');
    });
    
    await this.runTest('Constant Values', async () => {
      const constantData = Array.from({ length: 100 }, (_, i) => ({
        timestamp: new Date(Date.now() + i * 60000).toISOString(),
        value: 50
      }));
      
      const anomalies = await this.anomalyDetectionService.detectAnomalies(constantData, {
        algorithms: [ALGORITHMS.Z_SCORE],
        metric: 'constant_test'
      });
      
      this.assert(Array.isArray(anomalies), 'Should handle constant values');
      this.assert(anomalies.length === 0, 'Should not detect anomalies in constant data');
    });
  }
  
  /**
   * Run individual test
   * @param {string} name - Test name
   * @param {Function} testFn - Test function
   */
  async runTest(name, testFn) {
    this.results.total++;
    
    try {
      await testFn();
      this.results.passed++;
      this.results.tests.push({
        name,
        status: 'PASSED',
        timestamp: new Date().toISOString()
      });
      logger.info(`✓ ${name}`);
    } catch (error) {
      this.results.failed++;
      this.results.tests.push({
        name,
        status: 'FAILED',
        error: error.message,
        timestamp: new Date().toISOString()
      });
      logger.error(`✗ ${name}: ${error.message}`);
    }
  }
  
  /**
   * Assert condition
   * @param {boolean} condition - Condition to check
   * @param {string} message - Error message
   */
  assert(condition, message) {
    if (!condition) {
      throw new Error(message);
    }
  }
  
  /**
   * Cleanup test resources
   */
  async cleanup() {
    this.anomalyDetectionService.clearHistory();
    this.alertManager.shutdown();
  }
}

/**
 * Run predictive monitoring tests
 * @returns {Promise<Object>} Test results
 */
async function runPredictiveMonitoringTests() {
  const testSuite = new PredictiveMonitoringTestSuite();
  
  try {
    const results = await testSuite.runAllTests();
    await testSuite.cleanup();
    return results;
  } catch (error) {
    logger.error('Test suite error:', error);
    await testSuite.cleanup();
    throw error;
  }
}

// If script is run directly
if (require.main === module) {
  runPredictiveMonitoringTests()
    .then(results => {
      console.log('\n=== Predictive Monitoring Test Results ===');
      console.log(`Total Tests: ${results.total}`);
      console.log(`Passed: ${results.passed}`);
      console.log(`Failed: ${results.failed}`);
      console.log(`Success Rate: ${((results.passed / results.total) * 100).toFixed(1)}%`);
      
      if (results.failed > 0) {
        console.log('\nFailed Tests:');
        results.tests
          .filter(test => test.status === 'FAILED')
          .forEach(test => {
            console.log(`- ${test.name}: ${test.error}`);
          });
      }
      
      process.exit(results.failed === 0 ? 0 : 1);
    })
    .catch(error => {
      console.error('Test framework error:', error);
      process.exit(1);
    });
}

module.exports = {
  PredictiveMonitoringTestSuite,
  TestDataGenerator,
  runPredictiveMonitoringTests
};

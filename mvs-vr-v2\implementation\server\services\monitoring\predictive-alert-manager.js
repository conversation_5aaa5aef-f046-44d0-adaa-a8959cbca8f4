/**
 * Predictive Alert Manager
 *
 * This service manages predictive alerts based on forecasts and anomaly detection,
 * providing intelligent alert generation, routing, and escalation.
 */

import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { EventEmitter } from 'events';

const writeFileAsync = promisify(fs.writeFile);
const appendFileAsync = promisify(fs.appendFile);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

// Simple logger for services
const logger = {
  info: msg => console.log(`[INFO] ${msg}`),
  error: msg => console.error(`[ERROR] ${msg}`),
  debug: msg => console.log(`[DEBUG] ${msg}`),
};

// Alert types
const ALERT_TYPES = {
  PREDICTIVE: 'predictive',
  ANOMALY: 'anomaly',
  THRESHOLD: 'threshold',
  TREND: 'trend',
  SEASONAL: 'seasonal',
};

// Alert severities
const ALERT_SEVERITIES = {
  INFO: 'info',
  WARNING: 'warning',
  ERROR: 'error',
  CRITICAL: 'critical',
};

// Alert states
const ALERT_STATES = {
  ACTIVE: 'active',
  ACKNOWLEDGED: 'acknowledged',
  RESOLVED: 'resolved',
  SUPPRESSED: 'suppressed',
};

// Notification channels
const NOTIFICATION_CHANNELS = {
  EMAIL: 'email',
  SLACK: 'slack',
  WEBHOOK: 'webhook',
  SMS: 'sms',
  PAGERDUTY: 'pagerduty',
};

class PredictiveAlertManager extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      alertRetentionDays: 30,
      maxActiveAlerts: 1000,
      suppressionWindowMs: 5 * 60 * 1000, // 5 minutes
      escalationTimeoutMs: 15 * 60 * 1000, // 15 minutes
      outputPath: options.outputPath || path.join(__dirname, '../../data/alerts'),
      notificationChannels: options.notificationChannels || [NOTIFICATION_CHANNELS.EMAIL],
      ...options,
    };

    // Alert storage
    this.activeAlerts = new Map();
    this.alertHistory = [];
    this.suppressedAlerts = new Set();
    this.alertTemplates = new Map();

    // Statistics
    this.stats = {
      totalAlerts: 0,
      alertsByType: {},
      alertsBySeverity: {},
      alertsByState: {},
      suppressedCount: 0,
      escalatedCount: 0,
      lastAlert: null,
      startTime: Date.now(),
    };

    this.initialize();
  }

  /**
   * Initialize the alert manager
   */
  async initialize() {
    try {
      // Ensure output directory exists
      if (!(await existsAsync(this.options.outputPath))) {
        await mkdirAsync(this.options.outputPath, { recursive: true });
      }

      // Initialize statistics
      for (const type of Object.values(ALERT_TYPES)) {
        this.stats.alertsByType[type] = 0;
      }

      for (const severity of Object.values(ALERT_SEVERITIES)) {
        this.stats.alertsBySeverity[severity] = 0;
      }

      for (const state of Object.values(ALERT_STATES)) {
        this.stats.alertsByState[state] = 0;
      }

      // Load alert templates
      this.loadAlertTemplates();

      // Start cleanup interval
      this.cleanupInterval = setInterval(
        () => {
          this.cleanupOldAlerts();
        },
        60 * 60 * 1000,
      ); // Every hour

      logger.info('Predictive alert manager initialized');
    } catch (error) {
      logger.error('Failed to initialize predictive alert manager:', error);
      throw error;
    }
  }

  /**
   * Load alert templates
   */
  loadAlertTemplates() {
    const templates = {
      predictive_cpu_high: {
        title: 'High CPU Usage Predicted',
        description: 'CPU usage is predicted to exceed {threshold}% in the next {timeframe}',
        severity: ALERT_SEVERITIES.WARNING,
        channels: [NOTIFICATION_CHANNELS.EMAIL, NOTIFICATION_CHANNELS.SLACK],
        escalationTimeout: 10 * 60 * 1000, // 10 minutes
      },
      predictive_memory_high: {
        title: 'High Memory Usage Predicted',
        description: 'Memory usage is predicted to exceed {threshold}% in the next {timeframe}',
        severity: ALERT_SEVERITIES.WARNING,
        channels: [NOTIFICATION_CHANNELS.EMAIL, NOTIFICATION_CHANNELS.SLACK],
        escalationTimeout: 10 * 60 * 1000,
      },
      predictive_disk_full: {
        title: 'Disk Space Critical Predicted',
        description: 'Disk usage is predicted to exceed {threshold}% in the next {timeframe}',
        severity: ALERT_SEVERITIES.ERROR,
        channels: [
          NOTIFICATION_CHANNELS.EMAIL,
          NOTIFICATION_CHANNELS.SLACK,
          NOTIFICATION_CHANNELS.PAGERDUTY,
        ],
        escalationTimeout: 5 * 60 * 1000, // 5 minutes
      },
      anomaly_detected: {
        title: 'Anomaly Detected in {metric}',
        description:
          'Anomaly detected in {metric} with severity {severity} and confidence {confidence}%',
        severity: ALERT_SEVERITIES.WARNING,
        channels: [NOTIFICATION_CHANNELS.EMAIL],
        escalationTimeout: 15 * 60 * 1000,
      },
      critical_anomaly: {
        title: 'Critical Anomaly Detected',
        description: 'Critical anomaly detected in {metric} requiring immediate attention',
        severity: ALERT_SEVERITIES.CRITICAL,
        channels: [
          NOTIFICATION_CHANNELS.EMAIL,
          NOTIFICATION_CHANNELS.SLACK,
          NOTIFICATION_CHANNELS.PAGERDUTY,
          NOTIFICATION_CHANNELS.SMS,
        ],
        escalationTimeout: 2 * 60 * 1000, // 2 minutes
      },
      trend_change: {
        title: 'Significant Trend Change Detected',
        description: 'Significant trend change detected in {metric} with change score {score}',
        severity: ALERT_SEVERITIES.INFO,
        channels: [NOTIFICATION_CHANNELS.EMAIL],
        escalationTimeout: 30 * 60 * 1000, // 30 minutes
      },
    };

    for (const [key, template] of Object.entries(templates)) {
      this.alertTemplates.set(key, template);
    }

    logger.info(`Loaded ${this.alertTemplates.size} alert templates`);
  }

  /**
   * Generate predictive alert
   * @param {Object} prediction - Prediction data
   * @param {Object} options - Alert options
   * @returns {Object} Generated alert
   */
  generatePredictiveAlert(prediction, options = {}) {
    const {
      metric = 'unknown',
      threshold = 80,
      timeframe = '1 hour',
      templateKey = 'predictive_cpu_high',
    } = options;

    const template = this.alertTemplates.get(templateKey);
    if (!template) {
      throw new Error(`Alert template not found: ${templateKey}`);
    }

    const alert = {
      id: this.generateAlertId(),
      type: ALERT_TYPES.PREDICTIVE,
      severity: template.severity,
      state: ALERT_STATES.ACTIVE,
      metric,
      title: this.interpolateTemplate(template.title, { metric, threshold, timeframe }),
      description: this.interpolateTemplate(template.description, { metric, threshold, timeframe }),
      prediction,
      context: {
        threshold,
        timeframe,
        predictedValue: prediction.value,
        confidence: prediction.confidence,
        algorithm: prediction.algorithm,
      },
      channels: template.channels,
      escalationTimeout: template.escalationTimeout,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      acknowledgedAt: null,
      resolvedAt: null,
    };

    return this.processAlert(alert);
  }

  /**
   * Generate anomaly alert
   * @param {Object} anomaly - Anomaly data
   * @param {Object} options - Alert options
   * @returns {Object} Generated alert
   */
  generateAnomalyAlert(anomaly, options = {}) {
    const {
      metric = 'unknown',
      templateKey = anomaly.severity === 'critical' ? 'critical_anomaly' : 'anomaly_detected',
    } = options;

    const template = this.alertTemplates.get(templateKey);
    if (!template) {
      throw new Error(`Alert template not found: ${templateKey}`);
    }

    const alert = {
      id: this.generateAlertId(),
      type: ALERT_TYPES.ANOMALY,
      severity: this.mapAnomalySeverityToAlertSeverity(anomaly.severity),
      state: ALERT_STATES.ACTIVE,
      metric,
      title: this.interpolateTemplate(template.title, {
        metric,
        severity: anomaly.severity,
        confidence: Math.round(anomaly.confidence * 100),
      }),
      description: this.interpolateTemplate(template.description, {
        metric,
        severity: anomaly.severity,
        confidence: Math.round(anomaly.confidence * 100),
      }),
      anomaly,
      context: {
        anomalyType: anomaly.type,
        score: anomaly.score,
        confidence: anomaly.confidence,
        algorithm: anomaly.algorithm,
      },
      channels: template.channels,
      escalationTimeout: template.escalationTimeout,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      acknowledgedAt: null,
      resolvedAt: null,
    };

    return this.processAlert(alert);
  }

  /**
   * Process alert (deduplication, suppression, notification)
   * @param {Object} alert - Alert to process
   * @returns {Promise<Object>} Processed alert
   */
  async processAlert(alert) {
    // Check for suppression
    if (this.isAlertSuppressed(alert)) {
      logger.debug(`Alert suppressed: ${alert.id}`);
      this.stats.suppressedCount++;
      return null;
    }

    // Check for deduplication
    const existingAlert = this.findSimilarAlert(alert);
    if (existingAlert) {
      logger.debug(`Similar alert found, updating: ${existingAlert.id}`);
      return this.updateAlert(existingAlert.id, {
        updatedAt: new Date().toISOString(),
        context: { ...existingAlert.context, ...alert.context },
      });
    }

    // Store alert
    this.activeAlerts.set(alert.id, alert);
    this.alertHistory.push({ ...alert });

    // Update statistics
    this.updateStatistics(alert);

    // Send notifications
    this.sendNotifications(alert);

    // Schedule escalation
    this.scheduleEscalation(alert);

    // Emit event
    this.emit('alertGenerated', alert);

    // Save to file
    await this.saveAlert(alert);

    logger.info(`Alert generated: ${alert.id} - ${alert.title}`);

    return alert;
  }

  /**
   * Check if alert should be suppressed
   * @param {Object} alert - Alert to check
   * @returns {boolean} Whether alert is suppressed
   */
  isAlertSuppressed(alert) {
    const suppressionKey = `${alert.metric}-${alert.type}-${alert.severity}`;

    if (this.suppressedAlerts.has(suppressionKey)) {
      return true;
    }

    // Add to suppression list
    this.suppressedAlerts.add(suppressionKey);

    // Remove from suppression after window
    setTimeout(() => {
      this.suppressedAlerts.delete(suppressionKey);
    }, this.options.suppressionWindowMs);

    return false;
  }

  /**
   * Find similar active alert
   * @param {Object} alert - Alert to compare
   * @returns {Object|null} Similar alert or null
   */
  findSimilarAlert(alert) {
    for (const [_id, existingAlert] of this.activeAlerts.entries()) {
      if (
        existingAlert.metric === alert.metric &&
        existingAlert.type === alert.type &&
        existingAlert.severity === alert.severity &&
        existingAlert.state === ALERT_STATES.ACTIVE
      ) {
        return existingAlert;
      }
    }
    return null;
  }

  /**
   * Update existing alert
   * @param {string} alertId - Alert ID
   * @param {Object} updates - Updates to apply
   * @returns {Object} Updated alert
   */
  updateAlert(alertId, updates) {
    const alert = this.activeAlerts.get(alertId);
    if (!alert) {
      throw new Error(`Alert not found: ${alertId}`);
    }

    const updatedAlert = { ...alert, ...updates };
    this.activeAlerts.set(alertId, updatedAlert);

    this.emit('alertUpdated', updatedAlert);

    return updatedAlert;
  }

  /**
   * Acknowledge alert
   * @param {string} alertId - Alert ID
   * @param {string} acknowledgedBy - Who acknowledged the alert
   * @returns {Object} Acknowledged alert
   */
  acknowledgeAlert(alertId, acknowledgedBy = 'system') {
    const updates = {
      state: ALERT_STATES.ACKNOWLEDGED,
      acknowledgedAt: new Date().toISOString(),
      acknowledgedBy,
      updatedAt: new Date().toISOString(),
    };

    const alert = this.updateAlert(alertId, updates);
    this.stats.alertsByState[ALERT_STATES.ACKNOWLEDGED]++;
    this.stats.alertsByState[ALERT_STATES.ACTIVE]--;

    logger.info(`Alert acknowledged: ${alertId} by ${acknowledgedBy}`);

    return alert;
  }

  /**
   * Resolve alert
   * @param {string} alertId - Alert ID
   * @param {string} resolvedBy - Who resolved the alert
   * @returns {Object} Resolved alert
   */
  resolveAlert(alertId, resolvedBy = 'system') {
    const updates = {
      state: ALERT_STATES.RESOLVED,
      resolvedAt: new Date().toISOString(),
      resolvedBy,
      updatedAt: new Date().toISOString(),
    };

    const alert = this.updateAlert(alertId, updates);
    this.stats.alertsByState[ALERT_STATES.RESOLVED]++;

    // Remove from active alerts
    const currentState = this.activeAlerts.get(alertId)?.state;
    if (currentState) {
      this.stats.alertsByState[currentState]--;
    }

    this.activeAlerts.delete(alertId);

    logger.info(`Alert resolved: ${alertId} by ${resolvedBy}`);

    return alert;
  }

  /**
   * Send notifications for alert
   * @param {Object} alert - Alert to send notifications for
   */
  sendNotifications(alert) {
    for (const channel of alert.channels) {
      try {
        this.sendNotification(alert, channel);
      } catch (error) {
        logger.error(`Failed to send notification via ${channel}:`, error);
      }
    }
  }

  /**
   * Send notification via specific channel
   * @param {Object} alert - Alert to send
   * @param {string} channel - Notification channel
   */
  sendNotification(alert, channel) {
    // Placeholder implementation - would integrate with actual notification services
    logger.info(`Sending ${channel} notification for alert: ${alert.id}`);

    const notification = {
      channel,
      alert: {
        id: alert.id,
        title: alert.title,
        description: alert.description,
        severity: alert.severity,
        metric: alert.metric,
      },
      timestamp: new Date().toISOString(),
    };

    // Emit notification event for external handlers
    this.emit('notificationSent', notification);
  }

  /**
   * Schedule alert escalation
   * @param {Object} alert - Alert to schedule escalation for
   */
  scheduleEscalation(alert) {
    if (alert.escalationTimeout) {
      setTimeout(() => {
        const currentAlert = this.activeAlerts.get(alert.id);
        if (currentAlert && currentAlert.state === ALERT_STATES.ACTIVE) {
          this.escalateAlert(alert.id);
        }
      }, alert.escalationTimeout);
    }
  }

  /**
   * Escalate alert
   * @param {string} alertId - Alert ID to escalate
   */
  escalateAlert(alertId) {
    const alert = this.activeAlerts.get(alertId);
    if (!alert) {
      return;
    }

    // Increase severity
    const severityOrder = [
      ALERT_SEVERITIES.INFO,
      ALERT_SEVERITIES.WARNING,
      ALERT_SEVERITIES.ERROR,
      ALERT_SEVERITIES.CRITICAL,
    ];

    const currentIndex = severityOrder.indexOf(alert.severity);
    if (currentIndex < severityOrder.length - 1) {
      const newSeverity = severityOrder[currentIndex + 1];

      this.updateAlert(alertId, {
        severity: newSeverity,
        escalatedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      this.stats.escalatedCount++;

      logger.warn(`Alert escalated: ${alertId} to ${newSeverity}`);

      // Send escalation notifications
      this.sendNotifications(alert);
    }
  }

  /**
   * Interpolate template with variables
   * @param {string} template - Template string
   * @param {Object} variables - Variables to interpolate
   * @returns {string} Interpolated string
   */
  interpolateTemplate(template, variables) {
    return template.replace(/\{(\w+)\}/g, (match, key) => {
      return variables[key] !== undefined ? variables[key] : match;
    });
  }

  /**
   * Map anomaly severity to alert severity
   * @param {string} anomalySeverity - Anomaly severity
   * @returns {string} Alert severity
   */
  mapAnomalySeverityToAlertSeverity(anomalySeverity) {
    const mapping = {
      low: ALERT_SEVERITIES.INFO,
      medium: ALERT_SEVERITIES.WARNING,
      high: ALERT_SEVERITIES.ERROR,
      critical: ALERT_SEVERITIES.CRITICAL,
    };

    return mapping[anomalySeverity] || ALERT_SEVERITIES.WARNING;
  }

  /**
   * Generate unique alert ID
   * @returns {string} Unique alert ID
   */
  generateAlertId() {
    return `alert-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Update statistics
   * @param {Object} alert - Alert to update statistics for
   */
  updateStatistics(alert) {
    this.stats.totalAlerts++;
    this.stats.alertsByType[alert.type]++;
    this.stats.alertsBySeverity[alert.severity]++;
    this.stats.alertsByState[alert.state]++;
    this.stats.lastAlert = alert.createdAt;
  }

  /**
   * Save alert to file
   * @param {Object} alert - Alert to save
   */
  async saveAlert(alert) {
    try {
      const fileName = `alert-${alert.id}.json`;
      const filePath = path.join(this.options.outputPath, fileName);

      await writeFileAsync(filePath, JSON.stringify(alert, null, 2));

      // Also append to daily log
      const dateStr = new Date().toISOString().split('T')[0];
      const logFile = path.join(this.options.outputPath, `alerts-${dateStr}.jsonl`);
      await appendFileAsync(logFile, JSON.stringify(alert) + '\n');
    } catch (error) {
      logger.error('Failed to save alert:', error);
    }
  }

  /**
   * Clean up old alerts
   */
  cleanupOldAlerts() {
    const cutoffTime = Date.now() - this.options.alertRetentionDays * 24 * 60 * 60 * 1000;

    // Clean up alert history
    this.alertHistory = this.alertHistory.filter(alert => {
      return new Date(alert.createdAt).getTime() > cutoffTime;
    });

    // Clean up resolved alerts from active alerts
    for (const [id, alert] of this.activeAlerts.entries()) {
      if (
        alert.state === ALERT_STATES.RESOLVED &&
        new Date(alert.resolvedAt).getTime() < cutoffTime
      ) {
        this.activeAlerts.delete(id);
      }
    }

    logger.debug('Alert cleanup completed');
  }

  /**
   * Get alert statistics
   * @returns {Object} Alert statistics
   */
  getStatistics() {
    return {
      ...this.stats,
      uptime: Date.now() - this.stats.startTime,
      activeAlertsCount: this.activeAlerts.size,
      alertHistorySize: this.alertHistory.length,
      suppressedAlertsCount: this.suppressedAlerts.size,
    };
  }

  /**
   * Get active alerts
   * @param {Object} filters - Filters to apply
   * @returns {Array<Object>} Active alerts
   */
  getActiveAlerts(filters = {}) {
    let alerts = Array.from(this.activeAlerts.values());

    if (filters.severity) {
      alerts = alerts.filter(alert => alert.severity === filters.severity);
    }

    if (filters.type) {
      alerts = alerts.filter(alert => alert.type === filters.type);
    }

    if (filters.metric) {
      alerts = alerts.filter(alert => alert.metric === filters.metric);
    }

    return alerts.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
  }

  /**
   * Get alert history
   * @param {number} limit - Maximum number of alerts to return
   * @returns {Array<Object>} Alert history
   */
  getAlertHistory(limit = 100) {
    return this.alertHistory
      .slice(-limit)
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
  }

  /**
   * Cleanup and shutdown
   */
  shutdown() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    logger.info('Predictive alert manager shutdown');
  }
}

export {
  PredictiveAlertManager,
  ALERT_TYPES,
  ALERT_SEVERITIES,
  ALERT_STATES,
  NOTIFICATION_CHANNELS,
};

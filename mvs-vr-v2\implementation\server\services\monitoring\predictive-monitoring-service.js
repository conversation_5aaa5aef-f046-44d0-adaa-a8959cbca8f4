/**
 * Predictive Monitoring Service
 * 
 * This service integrates forecasting, anomaly detection, and alert management
 * to provide comprehensive predictive monitoring capabilities.
 */

const EventEmitter = require('events');
const { EnhancedAnomalyDetectionService, ALGORITHMS, SEVERITY_LEVELS } = require('./enhanced-anomaly-detection');
const { PredictiveAlertManager, ALERT_TYPES } = require('./predictive-alert-manager');

const logger = require('../../utils/logger').getLogger('predictive-monitoring-service');

// Monitoring configuration
const MONITORING_CONFIG = {
  enabled: process.env.PREDICTIVE_MONITORING_ENABLED !== 'false',
  checkInterval: parseInt(process.env.MONITORING_CHECK_INTERVAL || '60000', 10), // 1 minute
  forecastHorizon: parseInt(process.env.FORECAST_HORIZON || '3600000', 10), // 1 hour
  anomalyThreshold: parseFloat(process.env.ANOMALY_THRESHOLD || '3.0'),
  alertThreshold: parseFloat(process.env.ALERT_THRESHOLD || '80.0'),
  retentionPeriod: parseInt(process.env.MONITORING_RETENTION_DAYS || '30', 10)
};

// Monitored metrics configuration
const METRIC_CONFIGS = {
  cpu_usage: {
    algorithms: [ALGORITHMS.Z_SCORE, ALGORITHMS.MAD, ALGORITHMS.SEASONAL_DECOMPOSE],
    threshold: 80,
    alertTemplate: 'predictive_cpu_high',
    forecastEnabled: true,
    seasonality: true,
    seasonalPeriod: 24
  },
  memory_usage: {
    algorithms: [ALGORITHMS.Z_SCORE, ALGORITHMS.IQR],
    threshold: 85,
    alertTemplate: 'predictive_memory_high',
    forecastEnabled: true,
    seasonality: false
  },
  disk_usage: {
    algorithms: [ALGORITHMS.Z_SCORE, ALGORITHMS.CHANGE_POINT],
    threshold: 90,
    alertTemplate: 'predictive_disk_full',
    forecastEnabled: true,
    seasonality: false
  },
  response_time: {
    algorithms: [ALGORITHMS.Z_SCORE, ALGORITHMS.MAD, ALGORITHMS.IQR],
    threshold: 2000, // 2 seconds
    alertTemplate: 'anomaly_detected',
    forecastEnabled: false,
    seasonality: true,
    seasonalPeriod: 24
  },
  error_rate: {
    algorithms: [ALGORITHMS.Z_SCORE, ALGORITHMS.CHANGE_POINT],
    threshold: 5, // 5%
    alertTemplate: 'critical_anomaly',
    forecastEnabled: false,
    seasonality: false
  },
  request_count: {
    algorithms: [ALGORITHMS.SEASONAL_DECOMPOSE, ALGORITHMS.CHANGE_POINT],
    threshold: null, // No threshold-based alerts
    alertTemplate: 'trend_change',
    forecastEnabled: true,
    seasonality: true,
    seasonalPeriod: 24
  }
};

class PredictiveMonitoringService extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      ...MONITORING_CONFIG,
      ...options
    };
    
    // Initialize components
    this.anomalyDetectionService = new EnhancedAnomalyDetectionService({
      outputPath: options.anomalyOutputPath,
      visualizationEnabled: options.visualizationEnabled !== false,
      realTimeProcessing: true
    });
    
    this.alertManager = new PredictiveAlertManager({
      outputPath: options.alertOutputPath,
      notificationChannels: options.notificationChannels
    });
    
    // Data storage
    this.metricsData = new Map();
    this.forecastCache = new Map();
    this.monitoringIntervals = new Map();
    
    // Statistics
    this.stats = {
      totalMetrics: 0,
      totalAnomalies: 0,
      totalAlerts: 0,
      totalForecasts: 0,
      lastCheck: null,
      startTime: Date.now()
    };
    
    this.isInitialized = false;
  }
  
  /**
   * Initialize the predictive monitoring service
   */
  async initialize() {
    try {
      if (!this.options.enabled) {
        logger.warn('Predictive monitoring is disabled');
        return;
      }
      
      // Set up event listeners
      this.setupEventListeners();
      
      // Start monitoring for configured metrics
      this.startMonitoring();
      
      this.isInitialized = true;
      logger.info('Predictive monitoring service initialized');
    } catch (error) {
      logger.error('Failed to initialize predictive monitoring service:', error);
      throw error;
    }
  }
  
  /**
   * Set up event listeners
   */
  setupEventListeners() {
    // Listen for anomaly detection events
    this.anomalyDetectionService.on('anomalyDetected', async (anomaly) => {
      this.stats.totalAnomalies++;
      await this.handleAnomalyDetected(anomaly);
    });
    
    this.anomalyDetectionService.on('criticalAnomaly', async (anomaly) => {
      await this.handleCriticalAnomaly(anomaly);
    });
    
    // Listen for alert events
    this.alertManager.on('alertGenerated', (alert) => {
      this.stats.totalAlerts++;
      this.emit('alertGenerated', alert);
    });
    
    this.alertManager.on('notificationSent', (notification) => {
      this.emit('notificationSent', notification);
    });
  }
  
  /**
   * Start monitoring for all configured metrics
   */
  startMonitoring() {
    for (const [metricName, config] of Object.entries(METRIC_CONFIGS)) {
      this.startMetricMonitoring(metricName, config);
    }
    
    logger.info(`Started monitoring for ${Object.keys(METRIC_CONFIGS).length} metrics`);
  }
  
  /**
   * Start monitoring for a specific metric
   * @param {string} metricName - Metric name
   * @param {Object} config - Metric configuration
   */
  startMetricMonitoring(metricName, config) {
    const interval = setInterval(async () => {
      try {
        await this.checkMetric(metricName, config);
      } catch (error) {
        logger.error(`Error checking metric ${metricName}:`, error);
      }
    }, this.options.checkInterval);
    
    this.monitoringIntervals.set(metricName, interval);
    logger.debug(`Started monitoring for metric: ${metricName}`);
  }
  
  /**
   * Check a specific metric for anomalies and predictions
   * @param {string} metricName - Metric name
   * @param {Object} config - Metric configuration
   */
  async checkMetric(metricName, config) {
    // Get recent metric data
    const metricData = this.getMetricData(metricName);
    
    if (metricData.length < 10) {
      logger.debug(`Insufficient data for metric ${metricName}: ${metricData.length} points`);
      return;
    }
    
    // Detect anomalies
    const anomalies = await this.anomalyDetectionService.detectAnomalies(metricData, {
      algorithms: config.algorithms,
      metric: metricName,
      threshold: this.options.anomalyThreshold
    });
    
    // Process detected anomalies
    for (const anomaly of anomalies) {
      await this.processAnomaly(anomaly, metricName, config);
    }
    
    // Generate forecasts if enabled
    if (config.forecastEnabled) {
      await this.generateForecast(metricName, metricData, config);
    }
    
    this.stats.lastCheck = new Date().toISOString();
    this.stats.totalMetrics++;
  }
  
  /**
   * Process detected anomaly
   * @param {Object} anomaly - Detected anomaly
   * @param {string} metricName - Metric name
   * @param {Object} config - Metric configuration
   */
  async processAnomaly(anomaly, metricName, config) {
    try {
      // Generate alert for anomaly
      await this.alertManager.generateAnomalyAlert(anomaly, {
        metric: metricName,
        templateKey: config.alertTemplate
      });
      
      logger.info(`Anomaly detected in ${metricName}: severity=${anomaly.severity}, score=${anomaly.score.toFixed(2)}`);
    } catch (error) {
      logger.error(`Failed to process anomaly for ${metricName}:`, error);
    }
  }
  
  /**
   * Generate forecast for metric
   * @param {string} metricName - Metric name
   * @param {Array<Object>} metricData - Historical metric data
   * @param {Object} config - Metric configuration
   */
  async generateForecast(metricName, metricData, config) {
    try {
      // Simple exponential smoothing forecast
      const forecast = this.generateExponentialSmoothingForecast(metricData);
      
      // Cache forecast
      this.forecastCache.set(metricName, {
        forecast,
        generatedAt: new Date().toISOString(),
        horizon: this.options.forecastHorizon
      });
      
      // Check if forecast exceeds threshold
      if (config.threshold && forecast.value > config.threshold) {
        await this.alertManager.generatePredictiveAlert(forecast, {
          metric: metricName,
          threshold: config.threshold,
          timeframe: this.formatTimeframe(this.options.forecastHorizon),
          templateKey: config.alertTemplate
        });
      }
      
      this.stats.totalForecasts++;
      this.emit('forecastGenerated', { metric: metricName, forecast });
      
    } catch (error) {
      logger.error(`Failed to generate forecast for ${metricName}:`, error);
    }
  }
  
  /**
   * Generate exponential smoothing forecast
   * @param {Array<Object>} data - Historical data
   * @returns {Object} Forecast
   */
  generateExponentialSmoothingForecast(data) {
    if (data.length < 2) {
      throw new Error('Insufficient data for forecasting');
    }
    
    const alpha = 0.3; // Smoothing parameter
    let smoothedValue = data[0].value;
    
    // Apply exponential smoothing
    for (let i = 1; i < data.length; i++) {
      smoothedValue = alpha * data[i].value + (1 - alpha) * smoothedValue;
    }
    
    // Simple trend calculation
    const recentValues = data.slice(-5).map(d => d.value);
    const trend = recentValues.length > 1 
      ? (recentValues[recentValues.length - 1] - recentValues[0]) / (recentValues.length - 1)
      : 0;
    
    // Forecast next value
    const forecastValue = smoothedValue + trend;
    
    // Calculate confidence based on recent variance
    const variance = recentValues.reduce((sum, val) => sum + Math.pow(val - smoothedValue, 2), 0) / recentValues.length;
    const confidence = Math.max(0.1, Math.min(0.9, 1 - (Math.sqrt(variance) / smoothedValue)));
    
    return {
      value: Math.max(0, forecastValue),
      confidence,
      algorithm: 'exponential-smoothing',
      timestamp: new Date(Date.now() + this.options.forecastHorizon).toISOString(),
      context: {
        smoothedValue,
        trend,
        variance,
        dataPoints: data.length
      }
    };
  }
  
  /**
   * Add metric data point
   * @param {string} metricName - Metric name
   * @param {number} value - Metric value
   * @param {string} timestamp - Timestamp (optional)
   */
  addMetricData(metricName, value, timestamp = null) {
    if (!this.metricsData.has(metricName)) {
      this.metricsData.set(metricName, []);
    }
    
    const data = this.metricsData.get(metricName);
    
    data.push({
      timestamp: timestamp || new Date().toISOString(),
      value: parseFloat(value)
    });
    
    // Keep only recent data (based on retention period)
    const cutoffTime = Date.now() - (this.options.retentionPeriod * 24 * 60 * 60 * 1000);
    const filteredData = data.filter(point => new Date(point.timestamp).getTime() > cutoffTime);
    
    this.metricsData.set(metricName, filteredData);
  }
  
  /**
   * Get metric data
   * @param {string} metricName - Metric name
   * @param {number} limit - Maximum number of points to return
   * @returns {Array<Object>} Metric data
   */
  getMetricData(metricName, limit = 1000) {
    const data = this.metricsData.get(metricName) || [];
    return data.slice(-limit);
  }
  
  /**
   * Get forecast for metric
   * @param {string} metricName - Metric name
   * @returns {Object|null} Cached forecast
   */
  getForecast(metricName) {
    return this.forecastCache.get(metricName) || null;
  }
  
  /**
   * Handle anomaly detected event
   * @param {Object} anomaly - Detected anomaly
   */
  async handleAnomalyDetected(anomaly) {
    this.emit('anomalyDetected', anomaly);
    logger.debug(`Anomaly detected: ${anomaly.id}`);
  }
  
  /**
   * Handle critical anomaly event
   * @param {Object} anomaly - Critical anomaly
   */
  async handleCriticalAnomaly(anomaly) {
    this.emit('criticalAnomaly', anomaly);
    logger.warn(`Critical anomaly detected: ${anomaly.id}`);
  }
  
  /**
   * Format timeframe for display
   * @param {number} milliseconds - Timeframe in milliseconds
   * @returns {string} Formatted timeframe
   */
  formatTimeframe(milliseconds) {
    const minutes = Math.floor(milliseconds / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days} day${days > 1 ? 's' : ''}`;
    if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''}`;
    return `${minutes} minute${minutes > 1 ? 's' : ''}`;
  }
  
  /**
   * Get monitoring statistics
   * @returns {Object} Statistics
   */
  getStatistics() {
    const anomalyStats = this.anomalyDetectionService.getStatistics();
    const alertStats = this.alertManager.getStatistics();
    
    return {
      ...this.stats,
      uptime: Date.now() - this.stats.startTime,
      metricsCount: this.metricsData.size,
      forecastsCount: this.forecastCache.size,
      monitoredMetrics: Array.from(this.monitoringIntervals.keys()),
      anomalyDetection: anomalyStats,
      alertManagement: alertStats
    };
  }
  
  /**
   * Get health status
   * @returns {Object} Health status
   */
  getHealthStatus() {
    return {
      status: this.isInitialized ? 'healthy' : 'initializing',
      enabled: this.options.enabled,
      timestamp: new Date().toISOString(),
      components: {
        anomalyDetection: 'healthy',
        alertManagement: 'healthy',
        forecasting: 'healthy'
      },
      monitoredMetrics: Array.from(this.monitoringIntervals.keys()).length,
      activeAlerts: this.alertManager.getActiveAlerts().length
    };
  }
  
  /**
   * Stop monitoring
   */
  stopMonitoring() {
    // Clear all monitoring intervals
    for (const [metricName, interval] of this.monitoringIntervals.entries()) {
      clearInterval(interval);
      logger.debug(`Stopped monitoring for metric: ${metricName}`);
    }
    
    this.monitoringIntervals.clear();
    
    // Shutdown components
    this.alertManager.shutdown();
    
    logger.info('Predictive monitoring stopped');
  }
  
  /**
   * Add custom metric configuration
   * @param {string} metricName - Metric name
   * @param {Object} config - Metric configuration
   */
  addMetricConfig(metricName, config) {
    METRIC_CONFIGS[metricName] = config;
    
    if (this.isInitialized) {
      this.startMetricMonitoring(metricName, config);
    }
    
    logger.info(`Added custom metric configuration: ${metricName}`);
  }
  
  /**
   * Remove metric configuration
   * @param {string} metricName - Metric name
   */
  removeMetricConfig(metricName) {
    // Stop monitoring
    const interval = this.monitoringIntervals.get(metricName);
    if (interval) {
      clearInterval(interval);
      this.monitoringIntervals.delete(metricName);
    }
    
    // Remove configuration
    delete METRIC_CONFIGS[metricName];
    
    // Clear data
    this.metricsData.delete(metricName);
    this.forecastCache.delete(metricName);
    
    logger.info(`Removed metric configuration: ${metricName}`);
  }
}

module.exports = {
  PredictiveMonitoringService,
  MONITORING_CONFIG,
  METRIC_CONFIGS
};
